/**
 * 标签模板配置文件
 * 直接存放原有格式的模板数据
 */

const defaultTemplates = [
  //存储标签1
  {
    "TemplateName": "存储标签1",
    "Width": 50,
    "Height": 30,
    "Rotate": 1,
    "Copies": 1,
    "Density": 3,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 30,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/50x30_001.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 0,
        "Y": 0,
        "Width": 50,
        "Height": 30,
        "Content": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/50x30_001.png",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "IMAGE",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 17,
        "Y": 3,
        "Width": 33,
        "Height": 5,
        "Content": "品名",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "5",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      },
      {
        "AntiColor": false,
        "X": 17,
        "Y": 12,
        "Width": 30,
        "Height": 5,
        "Content": "操作人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "5",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      },
      {
        "AntiColor": false,
        "X": 17,
        "Y": 21,
        "Width": 33,
        "Height": 5,
        "Content": "日期",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "5",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE"
      }
    ]
  },
  //存储标签2
  {
    "TemplateName": "存储标签2",
    "Width": 50,
    "Height": 30,
    "Rotate": 1,
    "Copies": 1,
    "Density": 3,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 30,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/50x30_002.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 0,
        "Y": 0,
        "Width": 50,
        "Height": 30,
        "Content": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/50x30_002.png",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "IMAGE",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 21,
        "Y": 2,
        "Width": 33,
        "Height": 4,
        "Content": "品名",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      },
      {
        "AntiColor": false,
        "X": 21,
        "Y": 9,
        "Width": 30,
        "Height": 4,
        "Content": "最高存储量",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      },
      {
        "AntiColor": false,
        "X": 21,
        "Y": 16,
        "Width": 33,
        "Height": 4,
        "Content": "最低存储量",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      }, 
      {
        "AntiColor": false,
        "X": 21,
        "Y": 23,
        "Width": 33,
        "Height": 4,
        "Content": "责任人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      }
      
    ]
  },
  //食品留样标签1
  {
    "TemplateName": "食品留样标签1",
    "Width": 50,
    "Height": 30,
    "Rotate": 1,
    "Copies": 1,
    "Density": 3,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 30,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/50x30_003.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 0,
        "Y": 0,
        "Width": 50,
        "Height": 30,
        "Content": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/50x30_003.png",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "IMAGE",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 16,
        "Y": 5.5,
        "Width": 33,
        "Height": 4,
        "Content": "留样餐次",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "OPTION",
        "InputRegex": "|早餐 |午餐 |晚餐"
      },
      {
        "AntiColor": false,
        "X": 16,
        "Y": 11.5,
        "Width": 30,
        "Height": 4,
        "Content": "留样品名",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      },
      {
        "AntiColor": false,
        "X": 16,
        "Y": 17.5,
        "Width": 33,
        "Height": 4,
        "Content": "留样时间",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE",
        "InputMax": 6
      }, 
      {
        "AntiColor": false,
        "X": 16,
        "Y": 23.5,
        "Width": 33,
        "Height": 4,
        "Content": "留样人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      }
      
    ]
  },
  //食品留样标签2
  {
    "TemplateName": "食品留样标签2",
    "Width": 50,
    "Height": 30,
    "Rotate": 1,
    "Copies": 1,
    "Density": 3,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 30,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/50x30_004.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 0,
        "Y": 0,
        "Width": 50,
        "Height": 30,
        "Content": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/50x30_004.png",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "IMAGE",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 2.5,
        "Width": 33,
        "Height": 3,
        "Content": "留样餐次",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "OPTION",
        "InputRegex": "|早餐 |午餐 |晚餐"
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 7,
        "Width": 30,
        "Height": 3,
        "Content": "菜品",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      },
       {
        "AntiColor": false,
        "X": 14,
        "Y": 11.5,
        "Width": 30,
        "Height": 3,
        "Content": "重量",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 16,
        "Width": 33,
        "Height": 3,
        "Content": "时间",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE_TIME",
        "InputMax": 6
      }, 
      {
        "AntiColor": false,
        "X": 14,
        "Y": 20.5,
        "Width": 33,
        "Height": 3,
        "Content": "留样人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 25,
        "Width": 33,
        "Height": 3,
        "Content": "制作人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      }
    ]
  },
  //食材管控标签
  {
    "TemplateName": "食材管控标签",
    "Width": 50,
    "Height": 30,
    "Rotate": 0,
    "Copies": 1,
    "Density": 2,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 25,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/50x30_011.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 0,
        "Y": 0,
        "Width": 50,
        "Height": 30,
        "Content": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/50x30_011.png",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "IMAGE",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 4,
        "Width": 33,
        "Height": 5.5,
        "Content": "品名",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 8
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 8,
        "Width": 33,
        "Height": 5.5,
        "Content": "生产日期",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE",
        "InputMax": 8
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 12,
        "Width": 33,
        "Height": 5.5,
        "Content": "入库日期",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE"
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 16,
        "Width": 33,
        "Height": 5.5,
        "Content": "有效期至",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE"
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 20,
        "Width": 33,
        "Height": 5.5,
        "Content": "贮存条件",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "OPTION",
        "InputRegex": "|常温 |冷藏 |冷冻"
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 24,
        "Width": 33,
        "Height": 5.5,
        "Content": "填写人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 8
      },
    ]
  }
];

/**
 * 获取默认模板列表
 * @returns {Array} 模板数组的深拷贝
 */
function getDefaultTemplates() {
  return JSON.parse(JSON.stringify(defaultTemplates));
}

module.exports = {
  defaultTemplates,
  getDefaultTemplates
};
